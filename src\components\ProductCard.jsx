const ProductCard = ({ product, isSelected, onSelect }) => {
  return (
    <div
      onClick={onSelect}
      className={`border rounded-lg p-4 cursor-pointer transition-all duration-200 ${
        isSelected
          ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200'
          : 'border-gray-200 hover:border-gray-300 hover:shadow-md'
      }`}
    >
      <div className="flex justify-between items-start">
        <div className="flex-1">
          <h3 className="text-lg font-medium text-gray-900">
            {product.gas_type}
          </h3>
          <p className="text-sm text-gray-600 mt-1">
            {product.description || 'High-quality gas cylinder'}
          </p>
          <div className="mt-2 space-y-1">
            <p className="text-sm text-gray-500">
              <span className="font-medium">Seller:</span> {product.seller_name || 'N/A'}
            </p>
            <p className="text-sm text-gray-500">
              <span className="font-medium">Available:</span> {product.quantity_available} units
            </p>
            <p className="text-sm text-gray-500">
              <span className="font-medium">Location:</span> {product.location || 'Kigali'}
            </p>
          </div>
        </div>
        <div className="text-right">
          <p className="text-2xl font-bold text-blue-600">
            ${product.price}
          </p>
          <p className="text-sm text-gray-500">per unit</p>
          {product.quantity_available > 0 ? (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 mt-2">
              In Stock
            </span>
          ) : (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 mt-2">
              Out of Stock
            </span>
          )}
        </div>
      </div>
      
      {isSelected && (
        <div className="mt-3 pt-3 border-t border-blue-200">
          <p className="text-sm text-blue-600 font-medium">
            ✓ Selected for order
          </p>
        </div>
      )}
    </div>
  );
};

export default ProductCard;
