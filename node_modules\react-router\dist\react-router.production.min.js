/**
 * React Router v6.28.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
import*as e from"react";import{UNSAFE_invariant as t,joinPaths as r,matchPath as a,UNSAFE_decodePath as n,UNSAFE_getResolveToMatches as o,resolveTo as i,parsePath as l,matchRoutes as u,Action as s,UNSAFE_convertRouteMatchToUiMatch as c,stripBasename as d,IDLE_BLOCKER as p,isRouteErrorResponse as h,createMemoryHistory as m,AbortedDeferredError as v,createRouter as f}from"@remix-run/router";export{AbortedDeferredError,Action as NavigationType,createPath,defer,generatePath,isRouteErrorResponse,json,matchPath,matchRoutes,parsePath,redirect,redirectDocument,replace,resolvePath}from"@remix-run/router";const g=e.createContext(null),E=e.createContext(null),y=e.createContext(null),x=e.createContext(null),C=e.createContext(null),b=e.createContext({outlet:null,matches:[],isDataRoute:!1}),R=e.createContext(null);function _(a,{relative:n}={}){S()||t(!1);let{basename:o,navigator:i}=e.useContext(x),{hash:l,pathname:u,search:s}=O(a,{relative:n}),c=u;return"/"!==o&&(c="/"===u?o:r([o,u])),i.createHref({pathname:c,search:s,hash:l})}function S(){return null!=e.useContext(C)}function P(){return S()||t(!1),e.useContext(C).location}function U(){return e.useContext(C).navigationType}function k(r){S()||t(!1);let{pathname:o}=P();return e.useMemo((()=>a(r,n(o))),[o,r])}function D(t){e.useContext(x).static||e.useLayoutEffect(t)}function F(){let{isDataRoute:a}=e.useContext(b);return a?function(){let{router:t}=W(J.UseNavigateStable),r=Y($.UseNavigateStable),a=e.useRef(!1);return D((()=>{a.current=!0})),e.useCallback(((e,n={})=>{a.current&&("number"==typeof e?t.navigate(e):t.navigate(e,{fromRouteId:r,...n}))}),[t,r])}():function(){S()||t(!1);let a=e.useContext(g),{basename:n,future:l,navigator:u}=e.useContext(x),{matches:s}=e.useContext(b),{pathname:c}=P(),d=JSON.stringify(o(s,l.v7_relativeSplatPath)),p=e.useRef(!1);return D((()=>{p.current=!0})),e.useCallback(((e,t={})=>{if(!p.current)return;if("number"==typeof e)return void u.go(e);let o=i(e,JSON.parse(d),c,"path"===t.relative);null==a&&"/"!==n&&(o.pathname="/"===o.pathname?n:r([n,o.pathname])),(t.replace?u.replace:u.push)(o,t.state,t)}),[n,u,d,c,a])}()}const N=e.createContext(null);function B(){return e.useContext(N)}function L(t){let r=e.useContext(b).outlet;return r?e.createElement(N.Provider,{value:t},r):r}function A(){let{matches:t}=e.useContext(b),r=t[t.length-1];return r?r.params:{}}function O(t,{relative:r}={}){let{future:a}=e.useContext(x),{matches:n}=e.useContext(b),{pathname:l}=P(),u=JSON.stringify(o(n,a.v7_relativeSplatPath));return e.useMemo((()=>i(t,JSON.parse(u),l,"path"===r)),[t,u,l,r])}function j(e,t){return T(e,t)}function T(a,n,o,i){S()||t(!1);let{navigator:c}=e.useContext(x),{matches:d}=e.useContext(b),p=d[d.length-1],h=p?p.params:{};!p||p.pathname;let m=p?p.pathnameBase:"/";p&&p.route;let v,f=P();if(n){let e="string"==typeof n?l(n):n;"/"===m||e.pathname?.startsWith(m)||t(!1),v=e}else v=f;let g=v.pathname||"/",E=g;if("/"!==m){let e=m.replace(/^\//,"").split("/");E="/"+g.replace(/^\//,"").split("/").slice(e.length).join("/")}let y=u(a,{pathname:E}),R=z(y&&y.map((e=>Object.assign({},e,{params:Object.assign({},h,e.params),pathname:r([m,c.encodeLocation?c.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?m:r([m,c.encodeLocation?c.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),d,o,i);return n&&R?e.createElement(C.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...v},navigationType:s.Pop}},R):R}function M(){let t=te(),r=h(t)?`${t.status} ${t.statusText}`:t instanceof Error?t.message:JSON.stringify(t),a=t instanceof Error?t.stack:null,n={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return e.createElement(e.Fragment,null,e.createElement("h2",null,"Unexpected Application Error!"),e.createElement("h3",{style:{fontStyle:"italic"}},r),a?e.createElement("pre",{style:n},a):null,null)}const I=e.createElement(M,null);class H extends e.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?e.createElement(b.Provider,{value:this.props.routeContext},e.createElement(R.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function w({routeContext:t,match:r,children:a}){let n=e.useContext(g);return n&&n.static&&n.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(n.staticContext._deepestRenderedBoundaryId=r.route.id),e.createElement(b.Provider,{value:t},a)}function z(r,a=[],n=null,o=null){if(null==r){if(!n)return null;if(n.errors)r=n.matches;else{if(!(o?.v7_partialHydration&&0===a.length&&!n.initialized&&n.matches.length>0))return null;r=n.matches}}let i=r,l=n?.errors;if(null!=l){let e=i.findIndex((e=>e.route.id&&void 0!==l?.[e.route.id]));e>=0||t(!1),i=i.slice(0,Math.min(i.length,e+1))}let u=!1,s=-1;if(n&&o&&o.v7_partialHydration)for(let e=0;e<i.length;e++){let t=i[e];if((t.route.HydrateFallback||t.route.hydrateFallbackElement)&&(s=e),t.route.id){let{loaderData:e,errors:r}=n,a=t.route.loader&&void 0===e[t.route.id]&&(!r||void 0===r[t.route.id]);if(t.route.lazy||a){u=!0,i=s>=0?i.slice(0,s+1):[i[0]];break}}}return i.reduceRight(((t,r,o)=>{let c,d=!1,p=null,h=null;var m;n&&(c=l&&r.route.id?l[r.route.id]:void 0,p=r.route.errorElement||I,u&&(s<0&&0===o?(m="route-fallback",!1||ie[m]||(ie[m]=!0),d=!0,h=null):s===o&&(d=!0,h=r.route.hydrateFallbackElement||null)));let v=a.concat(i.slice(0,o+1)),f=()=>{let a;return a=c?p:d?h:r.route.Component?e.createElement(r.route.Component,null):r.route.element?r.route.element:t,e.createElement(w,{match:r,routeContext:{outlet:t,matches:v,isDataRoute:null!=n},children:a})};return n&&(r.route.ErrorBoundary||r.route.errorElement||0===o)?e.createElement(H,{location:n.location,revalidation:n.revalidation,component:p,error:c,children:f(),routeContext:{outlet:null,matches:v,isDataRoute:!0}}):f()}),null)}var J=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(J||{}),$=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}($||{});function W(r){let a=e.useContext(g);return a||t(!1),a}function V(r){let a=e.useContext(E);return a||t(!1),a}function Y(r){let a=function(r){let a=e.useContext(b);return a||t(!1),a}(),n=a.matches[a.matches.length-1];return n.route.id||t(!1),n.route.id}function q(){return Y($.UseRouteId)}function G(){return V($.UseNavigation).navigation}function K(){let t=W(J.UseRevalidator),r=V($.UseRevalidator);return e.useMemo((()=>({revalidate:t.router.revalidate,state:r.revalidation})),[t.router.revalidate,r.revalidation])}function Q(){let{matches:t,loaderData:r}=V($.UseMatches);return e.useMemo((()=>t.map((e=>c(e,r)))),[t,r])}function X(){let e=V($.UseLoaderData),t=Y($.UseLoaderData);if(!e.errors||null==e.errors[t])return e.loaderData[t];console.error(`You cannot \`useLoaderData\` in an errorElement (routeId: ${t})`)}function Z(e){return V($.UseRouteLoaderData).loaderData[e]}function ee(){let e=V($.UseActionData),t=Y($.UseLoaderData);return e.actionData?e.actionData[t]:void 0}function te(){let t=e.useContext(R),r=V($.UseRouteError),a=Y($.UseRouteError);return void 0!==t?t:r.errors?.[a]}function re(){return e.useContext(y)?._data}function ae(){return e.useContext(y)?._error}let ne=0;function oe(t){let{router:r,basename:a}=W(J.UseBlocker),n=V($.UseBlocker),[o,i]=e.useState(""),l=e.useCallback((e=>{if("function"!=typeof t)return!!t;if("/"===a)return t(e);let{currentLocation:r,nextLocation:n,historyAction:o}=e;return t({currentLocation:{...r,pathname:d(r.pathname,a)||r.pathname},nextLocation:{...n,pathname:d(n.pathname,a)||n.pathname},historyAction:o})}),[a,t]);return e.useEffect((()=>{let e=String(++ne);return i(e),()=>r.deleteBlocker(e)}),[r]),e.useEffect((()=>{""!==o&&r.getBlocker(o,l)}),[r,o,l]),o&&n.blockers.has(o)?n.blockers.get(o):p}const ie={};const le={};const ue=(e,t,r)=>{var a;le[a=`⚠️ React Router Future Flag Warning: ${t}. You can use the \`${e}\` future flag to opt-in early. For more information, see ${r}.`]||(le[a]=!0,console.warn(a))};function se(e,t){e?.v7_startTransition||ue("v7_startTransition","React Router will begin wrapping state updates in `React.startTransition` in v7","https://reactrouter.com/v6/upgrading/future#v7_starttransition"),e?.v7_relativeSplatPath||t&&t.v7_relativeSplatPath||ue("v7_relativeSplatPath","Relative route resolution within Splat routes is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath"),t&&(t.v7_fetcherPersist||ue("v7_fetcherPersist","The persistence behavior of fetchers is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_fetcherpersist"),t.v7_normalizeFormMethod||ue("v7_normalizeFormMethod","Casing of `formMethod` fields is being normalized to uppercase in v7","https://reactrouter.com/v6/upgrading/future#v7_normalizeformmethod"),t.v7_partialHydration||ue("v7_partialHydration","`RouterProvider` hydration behavior is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_partialhydration"),t.v7_skipActionErrorRevalidation||ue("v7_skipActionErrorRevalidation","The revalidation behavior after 4xx/5xx `action` responses is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_skipactionerrorrevalidation"))}const ce=e.startTransition;function de({fallbackElement:t,router:r,future:a}){let[n,o]=e.useState(r.state),{v7_startTransition:i}=a||{},l=e.useCallback((e=>{i&&ce?ce((()=>o(e))):o(e)}),[o,i]);e.useLayoutEffect((()=>r.subscribe(l)),[r,l]),e.useEffect((()=>{}),[]);let u=e.useMemo((()=>({createHref:r.createHref,encodeLocation:r.encodeLocation,go:e=>r.navigate(e),push:(e,t,a)=>r.navigate(e,{state:t,preventScrollReset:a?.preventScrollReset}),replace:(e,t,a)=>r.navigate(e,{replace:!0,state:t,preventScrollReset:a?.preventScrollReset})})),[r]),s=r.basename||"/",c=e.useMemo((()=>({router:r,navigator:u,static:!1,basename:s})),[r,u,s]);return e.useEffect((()=>se(a,r.future)),[r,a]),e.createElement(e.Fragment,null,e.createElement(g.Provider,{value:c},e.createElement(E.Provider,{value:n},e.createElement(ge,{basename:s,location:n.location,navigationType:n.historyAction,navigator:u,future:{v7_relativeSplatPath:r.future.v7_relativeSplatPath}},n.initialized||r.future.v7_partialHydration?e.createElement(pe,{routes:r.routes,future:r.future,state:n}):t))),null)}function pe({routes:e,future:t,state:r}){return T(e,void 0,r,t)}function he({basename:t,children:r,initialEntries:a,initialIndex:n,future:o}){let i=e.useRef();null==i.current&&(i.current=m({initialEntries:a,initialIndex:n,v5Compat:!0}));let l=i.current,[u,s]=e.useState({action:l.action,location:l.location}),{v7_startTransition:c}=o||{},d=e.useCallback((e=>{c&&ce?ce((()=>s(e))):s(e)}),[s,c]);return e.useLayoutEffect((()=>l.listen(d)),[l,d]),e.useEffect((()=>se(o)),[o]),e.createElement(ge,{basename:t,children:r,location:u.location,navigationType:u.action,navigator:l,future:o})}function me({to:r,replace:a,state:n,relative:l}){S()||t(!1);let{future:u,static:s}=e.useContext(x),{matches:c}=e.useContext(b),{pathname:d}=P(),p=F(),h=i(r,o(c,u.v7_relativeSplatPath),d,"path"===l),m=JSON.stringify(h);return e.useEffect((()=>p(JSON.parse(m),{replace:a,state:n,relative:l})),[p,m,l,a,n]),null}function ve(e){return L(e.context)}function fe(e){t(!1)}function ge({basename:r="/",children:a=null,location:n,navigationType:o=s.Pop,navigator:i,static:u=!1,future:c}){S()&&t(!1);let p=r.replace(/^\/*/,"/"),h=e.useMemo((()=>({basename:p,navigator:i,static:u,future:{v7_relativeSplatPath:!1,...c}})),[p,c,i,u]);"string"==typeof n&&(n=l(n));let{pathname:m="/",search:v="",hash:f="",state:g=null,key:E="default"}=n,y=e.useMemo((()=>{let e=d(m,p);return null==e?null:{location:{pathname:e,search:v,hash:f,state:g,key:E},navigationType:o}}),[p,m,v,f,g,E,o]);return null==y?null:e.createElement(x.Provider,{value:h},e.createElement(C.Provider,{children:a,value:y}))}function Ee({children:e,location:t}){return j(_e(e),t)}function ye({children:t,errorElement:r,resolve:a}){return e.createElement(be,{resolve:a,errorElement:r},e.createElement(Re,null,t))}var xe=function(e){return e[e.pending=0]="pending",e[e.success=1]="success",e[e.error=2]="error",e}(xe||{});const Ce=new Promise((()=>{}));class be extends e.Component{constructor(e){super(e),this.state={error:null}}static getDerivedStateFromError(e){return{error:e}}componentDidCatch(e,t){console.error("<Await> caught the following error during render",e,t)}render(){let{children:t,errorElement:r,resolve:a}=this.props,n=null,o=xe.pending;if(a instanceof Promise)if(this.state.error){o=xe.error;let e=this.state.error;n=Promise.reject().catch((()=>{})),Object.defineProperty(n,"_tracked",{get:()=>!0}),Object.defineProperty(n,"_error",{get:()=>e})}else a._tracked?(n=a,o="_error"in n?xe.error:"_data"in n?xe.success:xe.pending):(o=xe.pending,Object.defineProperty(a,"_tracked",{get:()=>!0}),n=a.then((e=>Object.defineProperty(a,"_data",{get:()=>e})),(e=>Object.defineProperty(a,"_error",{get:()=>e}))));else o=xe.success,n=Promise.resolve(),Object.defineProperty(n,"_tracked",{get:()=>!0}),Object.defineProperty(n,"_data",{get:()=>a});if(o===xe.error&&n._error instanceof v)throw Ce;if(o===xe.error&&!r)throw n._error;if(o===xe.error)return e.createElement(y.Provider,{value:n,children:r});if(o===xe.success)return e.createElement(y.Provider,{value:n,children:t});throw n}}function Re({children:t}){let r=re(),a="function"==typeof t?t(r):t;return e.createElement(e.Fragment,null,a)}function _e(r,a=[]){let n=[];return e.Children.forEach(r,((r,o)=>{if(!e.isValidElement(r))return;let i=[...a,o];if(r.type===e.Fragment)return void n.push.apply(n,_e(r.props.children,i));r.type!==fe&&t(!1),r.props.index&&r.props.children&&t(!1);let l={id:r.props.id||i.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:null!=r.props.ErrorBoundary||null!=r.props.errorElement,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(l.children=_e(r.props.children,i)),n.push(l)})),n}function Se(e){return z(e)}function Pe(t){let r={hasErrorBoundary:null!=t.ErrorBoundary||null!=t.errorElement};return t.Component&&Object.assign(r,{element:e.createElement(t.Component),Component:void 0}),t.HydrateFallback&&Object.assign(r,{hydrateFallbackElement:e.createElement(t.HydrateFallback),HydrateFallback:void 0}),t.ErrorBoundary&&Object.assign(r,{errorElement:e.createElement(t.ErrorBoundary),ErrorBoundary:void 0}),r}function Ue(e,t){return f({basename:t?.basename,future:{...t?.future,v7_prependBasename:!0},history:m({initialEntries:t?.initialEntries,initialIndex:t?.initialIndex}),hydrationData:t?.hydrationData,routes:e,mapRouteProperties:Pe,dataStrategy:t?.dataStrategy,patchRoutesOnNavigation:t?.patchRoutesOnNavigation}).initialize()}export{ye as Await,he as MemoryRouter,me as Navigate,ve as Outlet,fe as Route,ge as Router,de as RouterProvider,Ee as Routes,g as UNSAFE_DataRouterContext,E as UNSAFE_DataRouterStateContext,C as UNSAFE_LocationContext,x as UNSAFE_NavigationContext,b as UNSAFE_RouteContext,se as UNSAFE_logV6DeprecationWarnings,Pe as UNSAFE_mapRouteProperties,q as UNSAFE_useRouteId,T as UNSAFE_useRoutesImpl,Ue as createMemoryRouter,_e as createRoutesFromChildren,_e as createRoutesFromElements,Se as renderMatches,ee as useActionData,ae as useAsyncError,re as useAsyncValue,oe as useBlocker,_ as useHref,S as useInRouterContext,X as useLoaderData,P as useLocation,k as useMatch,Q as useMatches,F as useNavigate,G as useNavigation,U as useNavigationType,L as useOutlet,B as useOutletContext,A as useParams,O as useResolvedPath,K as useRevalidator,te as useRouteError,Z as useRouteLoaderData,j as useRoutes};
//# sourceMappingURL=react-router.production.min.js.map
