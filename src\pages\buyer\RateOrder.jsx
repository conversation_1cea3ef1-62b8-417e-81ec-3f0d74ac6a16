import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { buyerService } from '../../services/endpoints';

const RateOrder = () => {
  const [orders, setOrders] = useState([]);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [rating, setRating] = useState(5);
  const [comment, setComment] = useState('');
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    fetchDeliveredOrders();
  }, []);

  const fetchDeliveredOrders = async () => {
    try {
      const response = await buyerService.getOrders();
      // Filter only delivered orders that haven't been rated
      const deliveredOrders = response.filter(order => 
        order.status === 'delivered' && !order.is_rated
      );
      setOrders(deliveredOrders);
    } catch (error) {
      setError('Failed to fetch orders');
      console.error('Error fetching orders:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!selectedOrder) {
      setError('Please select an order to rate');
      return;
    }

    setSubmitting(true);
    setError('');

    try {
      const ratingData = {
        order_id: selectedOrder.id,
        rating: rating,
        comment: comment
      };

      await buyerService.createRating(ratingData);
      navigate('/buyer/orders', { 
        state: { message: 'Rating submitted successfully!' }
      });
    } catch (error) {
      setError(error.response?.data?.message || 'Failed to submit rating');
    } finally {
      setSubmitting(false);
    }
  };

  const StarRating = ({ rating, onRatingChange }) => {
    return (
      <div className="flex space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            onClick={() => onRatingChange(star)}
            className={`text-2xl ${
              star <= rating ? 'text-yellow-400' : 'text-gray-300'
            } hover:text-yellow-400 transition-colors`}
          >
            ★
          </button>
        ))}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-gray-900">Rate Your Orders</h1>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {orders.length === 0 ? (
        <div className="bg-white shadow rounded-lg p-8">
          <div className="text-center">
            <div className="text-gray-400 text-6xl mb-4">⭐</div>
            <p className="text-gray-500 text-lg mb-4">No orders to rate</p>
            <p className="text-gray-400">Complete some orders first to leave ratings</p>
          </div>
        </div>
      ) : (
        <div className="grid lg:grid-cols-2 gap-8">
          {/* Order Selection */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Select Order to Rate
            </h2>
            
            <div className="space-y-3">
              {orders.map((order) => (
                <div
                  key={order.id}
                  onClick={() => setSelectedOrder(order)}
                  className={`border rounded-lg p-4 cursor-pointer transition-all duration-200 ${
                    selectedOrder?.id === order.id
                      ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200'
                      : 'border-gray-200 hover:border-gray-300 hover:shadow-md'
                  }`}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-medium text-gray-900">
                        Order #{order.id}
                      </h3>
                      <p className="text-sm text-gray-600">
                        {order.gas_type} - Quantity: {order.quantity}
                      </p>
                      <p className="text-sm text-gray-500">
                        Seller: {order.seller_name || 'N/A'}
                      </p>
                      <p className="text-sm text-gray-500">
                        Delivered: {new Date(order.delivery_date || order.updated_at).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-gray-900">
                        ${order.total_amount?.toFixed(2)}
                      </p>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Delivered
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Rating Form */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Rate Your Experience
            </h2>
            
            {selectedOrder ? (
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="bg-gray-50 p-4 rounded-md">
                  <h3 className="font-medium text-gray-900">Selected Order</h3>
                  <p className="text-sm text-gray-600">Order #{selectedOrder.id}</p>
                  <p className="text-sm text-gray-600">{selectedOrder.gas_type}</p>
                  <p className="text-sm text-gray-600">Seller: {selectedOrder.seller_name}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Rating
                  </label>
                  <StarRating rating={rating} onRatingChange={setRating} />
                  <p className="text-sm text-gray-500 mt-1">
                    {rating === 1 && 'Very Poor'}
                    {rating === 2 && 'Poor'}
                    {rating === 3 && 'Average'}
                    {rating === 4 && 'Good'}
                    {rating === 5 && 'Excellent'}
                  </p>
                </div>
                
                <div>
                  <label htmlFor="comment" className="block text-sm font-medium text-gray-700">
                    Comment (Optional)
                  </label>
                  <textarea
                    id="comment"
                    rows={4}
                    value={comment}
                    onChange={(e) => setComment(e.target.value)}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="Share your experience with this order..."
                  />
                </div>
                
                <button
                  type="submit"
                  disabled={submitting}
                  className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white py-2 px-4 rounded-md font-medium"
                >
                  {submitting ? 'Submitting Rating...' : 'Submit Rating'}
                </button>
              </form>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500">Select an order to rate</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default RateOrder;
