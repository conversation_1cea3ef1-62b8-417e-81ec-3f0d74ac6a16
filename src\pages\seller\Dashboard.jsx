import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { sellerService } from '../../services/endpoints';
import DashboardStats from '../../components/DashboardStats';

const SellerDashboard = () => {
  const [dashboardData, setDashboardData] = useState({
    orders: [],
    stock: [],
    totalRevenue: 0,
    pendingOrders: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const [dashboardResponse, ordersResponse, stockResponse] = await Promise.all([
        sellerService.getDashboard(),
        sellerService.getOrders(),
        sellerService.getStock()
      ]);

      setDashboardData({
        ...dashboardResponse,
        orders: ordersResponse.slice(0, 5), // Recent orders
        stock: stockResponse
      });
    } catch (error) {
      setError('Failed to fetch dashboard data');
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const stats = [
    {
      title: 'Total Orders',
      value: dashboardData.orders.length,
      icon: '📋',
      color: 'bg-blue-500'
    },
    {
      title: 'Pending Orders',
      value: dashboardData.orders.filter(order => order.status === 'pending').length,
      icon: '⏳',
      color: 'bg-yellow-500'
    },
    {
      title: 'Stock Items',
      value: dashboardData.stock.length,
      icon: '📦',
      color: 'bg-green-500'
    },
    {
      title: 'Total Revenue',
      value: `$${dashboardData.orders.reduce((sum, order) => sum + (order.total_amount || 0), 0).toFixed(2)}`,
      icon: '💰',
      color: 'bg-purple-500'
    }
  ];

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-gray-900">Seller Dashboard</h1>
        <div className="flex space-x-3">
          <Link
            to="/seller/inventory"
            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md font-medium"
          >
            Manage Inventory
          </Link>
          <Link
            to="/seller/orders"
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium"
          >
            View Orders
          </Link>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      <DashboardStats stats={stats} />

      <div className="grid lg:grid-cols-2 gap-6">
        {/* Recent Orders */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Recent Orders
            </h3>
            
            {dashboardData.orders.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">No orders yet</p>
              </div>
            ) : (
              <div className="space-y-3">
                {dashboardData.orders.map((order) => (
                  <div key={order.id} className="flex justify-between items-center p-3 bg-gray-50 rounded-md">
                    <div>
                      <p className="font-medium text-gray-900">Order #{order.id}</p>
                      <p className="text-sm text-gray-600">{order.gas_type} - {order.quantity} units</p>
                      <p className="text-sm text-gray-500">Customer: {order.buyer_name || 'N/A'}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-gray-900">${order.total_amount?.toFixed(2)}</p>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        order.status === 'delivered' ? 'bg-green-100 text-green-800' :
                        order.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        order.status === 'confirmed' ? 'bg-blue-100 text-blue-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {order.status}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            )}
            
            {dashboardData.orders.length > 0 && (
              <div className="mt-4 text-center">
                <Link
                  to="/seller/orders"
                  className="text-blue-600 hover:text-blue-500 font-medium"
                >
                  View All Orders
                </Link>
              </div>
            )}
          </div>
        </div>

        {/* Stock Overview */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Stock Overview
            </h3>
            
            {dashboardData.stock.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">No stock items</p>
                <Link
                  to="/seller/inventory"
                  className="mt-2 inline-block bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md"
                >
                  Add Stock
                </Link>
              </div>
            ) : (
              <div className="space-y-3">
                {dashboardData.stock.slice(0, 5).map((item) => (
                  <div key={item.id} className="flex justify-between items-center p-3 bg-gray-50 rounded-md">
                    <div>
                      <p className="font-medium text-gray-900">{item.gas_type}</p>
                      <p className="text-sm text-gray-600">Price: ${item.price}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-gray-900">{item.quantity_available} units</p>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        item.quantity_available > 10 ? 'bg-green-100 text-green-800' :
                        item.quantity_available > 0 ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {item.quantity_available > 10 ? 'In Stock' :
                         item.quantity_available > 0 ? 'Low Stock' : 'Out of Stock'}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            )}
            
            {dashboardData.stock.length > 0 && (
              <div className="mt-4 text-center">
                <Link
                  to="/seller/inventory"
                  className="text-blue-600 hover:text-blue-500 font-medium"
                >
                  Manage Inventory
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SellerDashboard;
