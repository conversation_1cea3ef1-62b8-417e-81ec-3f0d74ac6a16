
**Frontend Development Prompt: Kigali GasGo – Fresh React Rebuild**

**Project Name:** Kigali GasGo (GasGod Rwanda)
**Frontend Framework:** React (with Vite)
**Styling:** Tailwind CSS
**Start From:** Scratch – this is a **fresh React + Vite project**, not a Vue conversion
**Backend API:** [gas\_stock\_management\_backend](https://github.com/Alicelinzy/gas_stock_management_backend)
**Docker Backend:** Yes – Docker-based backend is working
**API Documentation:**
→ [API Docs in Repo](https://github.com/Alicelinzy/gas_stock_management_backend/blob/main/backend/docs/gas_management/api_documentation.md)
→ Endpoints listed in full below (for reference)

---

### 1. 🧩 **Goal and Context**

Build a brand new frontend using **React + Vite** for the Kigali GasGo system – a platform for gas delivery and stock management. The frontend must integrate tightly with the backend's API and support 3 user roles: **Buyers, Sellers, and <PERSON><PERSON>**.

The Vue project is deprecated – start clean with modern React.

---

### 2. ⚙️ **Tech Stack and Architecture**

* **React + Vite** (lightweight and fast builds)
* **Tailwind CSS** for styling
* **React Router v6** for routing
* **Axios** for API requests
* **JWT Auth**: token stored in `localStorage`
* Role-based routing and route guards
* Use `.env` for `VITE_API_BASE_URL`

---

### 3. 👥 **User Roles and Core Pages**

#### Public:

* `/` – Landing page
* `/signup` – Sign up (Buyer or Seller)
* `/login` – Login

#### Buyer:

* `/buyer/dashboard` – View gas products
* `/buyer/order` – Place an order
* `/buyer/orders` – Track orders
* `/buyer/ratings/order/:id` – Rate order

#### Seller:

* `/seller/dashboard` – Stats overview
* `/seller/inventory` – Manage gas stock
* `/seller/orders` – Fulfill orders
* `/seller/invoice/generate/:id` – Generate invoice

#### Admin:

* `/admin/dashboard` – Overview
* `/admin/orders` – Review and verify orders
* `/admin/invoices` – Approve invoices
* `/admin/users` – Manage users
* `/admin/reports` – See system activity

---

### 4. 🔌 **Backend API Integration**

Backend: [`https://github.com/Alicelinzy/gas_stock_management_backend`](https://github.com/Alicelinzy/gas_stock_management_backend)
Docs: [`API Documentation`](https://github.com/Alicelinzy/gas_stock_management_backend/blob/main/backend/docs/gas_management/api_documentation.md)

Use the actual endpoint structure provided. Axios interceptors must attach JWTs to all authenticated requests.

---

### 5. 🗂️ **API Endpoints Summary**

#### 🔐 Accounts

* `POST /api/accounts/register/`
* `POST /api/accounts/login/`
* `POST /api/accounts/logout/`
* `GET /api/accounts/profile/`
* `PUT /api/accounts/profile/update/`
* `POST /api/accounts/change-password/`

#### 🧑‍💼 Admin

* `GET /api/admin/orders/`
* `GET /api/admin/orders/{order_id}/`
* `PUT /api/admin/orders/update-status/{order_id}/`
* `GET /api/admin/invoices/`
* `PUT /api/admin/invoices/update-status/{invoice_id}/`
* `GET /api/admin/users/`
* `PUT /api/admin/users/{user_id}/update-role/`

#### 🧾 Invoices

* `GET /api/invoices/`
* `GET /api/invoices/{id}/`
* `POST /api/invoices/create/`
* `PUT /api/invoices/update/{id}/`
* `DELETE /api/invoices/delete/{id}/`

#### 🛍️ Buyer

* `GET /api/buyer/products/`
* `POST /api/buyer/orders/create/`
* `GET /api/buyer/orders/`
* `GET /api/buyer/orders/{id}/`
* `POST /api/buyer/ratings/create/`
* `GET /api/buyer/ratings/`

#### 🏪 Seller

* `GET /api/seller/dashboard/`
* `GET /api/seller/stock/`
* `POST /api/seller/stock/create/`
* `PUT /api/seller/stock/update/{id}/`
* `DELETE /api/seller/stock/delete/{id}/`
* `GET /api/seller/orders/`
* `PUT /api/seller/orders/update-status/{order_id}/`
* `POST /api/seller/invoices/create/`
* `GET /api/seller/invoices/`

---

### 6. 📐 UI/UX Guidelines

* Responsive: Mobile, tablet, desktop
* Validation for forms
* Loading spinners and error messages
* Use Tailwind utilities (`grid`, `flex`, `gap`, etc.)
* Reusable components:

  * `<ProductCard />`
  * `<Sidebar />`
  * `<DashboardStats />`
  * `<OrderTable />`

---

### 7. ✅ Final Instructions

* Fully replace Vue frontend with **clean React app**
* Use **React functional components** only
* All routes must work with real backend APIs
* Ensure Docker backend integration works locally
* Use clear, modular structure: `/pages`, `/components`, `/services`
* Keep the UI clean and functional — no over-design needed
