import api from './api';

// Buyer endpoints
export const buyerService = {
  getProducts: async () => {
    const response = await api.get('/api/buyer/products/');
    return response.data;
  },

  createOrder: async (orderData) => {
    const response = await api.post('/api/buyer/orders/create/', orderData);
    return response.data;
  },

  getOrders: async () => {
    const response = await api.get('/api/buyer/orders/');
    return response.data;
  },

  getOrder: async (id) => {
    const response = await api.get(`/api/buyer/orders/${id}/`);
    return response.data;
  },

  createRating: async (ratingData) => {
    const response = await api.post('/api/buyer/ratings/create/', ratingData);
    return response.data;
  },

  getRatings: async () => {
    const response = await api.get('/api/buyer/ratings/');
    return response.data;
  }
};

// Seller endpoints
export const sellerService = {
  getDashboard: async () => {
    const response = await api.get('/api/seller/dashboard/');
    return response.data;
  },

  getStock: async () => {
    const response = await api.get('/api/seller/stock/');
    return response.data;
  },

  createStock: async (stockData) => {
    const response = await api.post('/api/seller/stock/create/', stockData);
    return response.data;
  },

  updateStock: async (id, stockData) => {
    const response = await api.put(`/api/seller/stock/update/${id}/`, stockData);
    return response.data;
  },

  deleteStock: async (id) => {
    const response = await api.delete(`/api/seller/stock/delete/${id}/`);
    return response.data;
  },

  getOrders: async () => {
    const response = await api.get('/api/seller/orders/');
    return response.data;
  },

  updateOrderStatus: async (orderId, statusData) => {
    const response = await api.put(`/api/seller/orders/update-status/${orderId}/`, statusData);
    return response.data;
  },

  createInvoice: async (invoiceData) => {
    const response = await api.post('/api/seller/invoices/create/', invoiceData);
    return response.data;
  },

  getInvoices: async () => {
    const response = await api.get('/api/seller/invoices/');
    return response.data;
  }
};

// Admin endpoints
export const adminService = {
  getOrders: async () => {
    const response = await api.get('/api/admin/orders/');
    return response.data;
  },

  getOrder: async (orderId) => {
    const response = await api.get(`/api/admin/orders/${orderId}/`);
    return response.data;
  },

  updateOrderStatus: async (orderId, statusData) => {
    const response = await api.put(`/api/admin/orders/update-status/${orderId}/`, statusData);
    return response.data;
  },

  getInvoices: async () => {
    const response = await api.get('/api/admin/invoices/');
    return response.data;
  },

  updateInvoiceStatus: async (invoiceId, statusData) => {
    const response = await api.put(`/api/admin/invoices/update-status/${invoiceId}/`, statusData);
    return response.data;
  },

  getUsers: async () => {
    const response = await api.get('/api/admin/users/');
    return response.data;
  },

  updateUserRole: async (userId, roleData) => {
    const response = await api.put(`/api/admin/users/${userId}/update-role/`, roleData);
    return response.data;
  }
};

// Invoice endpoints
export const invoiceService = {
  getInvoices: async () => {
    const response = await api.get('/api/invoices/');
    return response.data;
  },

  getInvoice: async (id) => {
    const response = await api.get(`/api/invoices/${id}/`);
    return response.data;
  },

  createInvoice: async (invoiceData) => {
    const response = await api.post('/api/invoices/create/', invoiceData);
    return response.data;
  },

  updateInvoice: async (id, invoiceData) => {
    const response = await api.put(`/api/invoices/update/${id}/`, invoiceData);
    return response.data;
  },

  deleteInvoice: async (id) => {
    const response = await api.delete(`/api/invoices/delete/${id}/`);
    return response.data;
  }
};
